#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟玩家插入时机验证脚本
用于验证虚拟玩家是否按照配置的stage_for_real_players参数正确插入游戏
"""

import sqlite3
import yaml

def load_config():
    """加载配置文件获取stage_for_real_players参数"""
    try:
        with open('config_play.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config['virtual_player']['stage_for_real_players']
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return 7  # 默认值

def analyze_game_timing():
    """分析游戏记录中的虚拟玩家插入时机"""
    try:
        conn = sqlite3.connect('Play_db.db')
        cursor = conn.cursor()
        
        # 获取配置参数
        stage_for_real_players = load_config()
        print(f"配置参数 stage_for_real_players: {stage_for_real_players}")
        
        # 获取所有游戏记录，按时间顺序排列
        cursor.execute('SELECT player_id, result, game_time FROM games ORDER BY game_time ASC')
        games = cursor.fetchall()
        
        if not games:
            print("没有找到游戏记录")
            return
        
        print(f"\n总游戏记录数: {len(games)}")
        print("=" * 80)
        
        # 分析虚拟玩家插入模式
        real_count = 0
        virtual_count = 0
        total_count = 0
        virtual_intervals = []
        
        print("游戏序列分析:")
        print("时间\t\t\t玩家类型\t玩家ID\t\t\t前面真实玩家数")
        print("-" * 80)
        
        for player_id, _, game_time in games:
            total_count += 1
            is_virtual = 'Virtual' in player_id
            
            if is_virtual:
                virtual_count += 1
                virtual_intervals.append(real_count)
                player_type = "虚拟"
                print(f"{game_time}\t{player_type}\t{player_id[:20]:<20}\t{real_count}")
                real_count = 0  # 重置计数
            else:
                real_count += 1
                player_type = "真实"
                print(f"{game_time}\t{player_type}\t{player_id[:20]:<20}\t-")
        
        # 统计分析
        print("\n" + "=" * 80)
        print("统计分析:")
        print(f"总游戏数: {total_count}")
        print(f"真实玩家游戏数: {total_count - virtual_count}")
        print(f"虚拟玩家游戏数: {virtual_count}")
        
        if virtual_intervals:
            print(f"\n虚拟玩家插入间隔分析:")
            print(f"虚拟玩家插入次数: {len(virtual_intervals)}")
            print(f"插入间隔列表: {virtual_intervals}")
            
            if len(virtual_intervals) > 0:
                avg_interval = sum(virtual_intervals) / len(virtual_intervals)
                print(f"平均插入间隔: {avg_interval:.2f}")
                
                # 根据当前队列状态判断模式
                cursor.execute('SELECT COUNT(*) FROM queue WHERE player_id NOT LIKE "Virtual%"')
                current_real_players = cursor.fetchone()[0]

                print(f"\n当前模式分析:")
                print(f"当前队列真实玩家数: {current_real_players}")

                if current_real_players <= 2:
                    mode = "填充模式"
                    print(f"模式: {mode}")
                    print(f"填充模式特征: 虚拟玩家通过定时机制持续填充，不严格按间隔插入")
                    print(f"实际平均间隔: {avg_interval:.2f} (接近0是正常的)")

                    # 填充模式验证：检查虚拟玩家是否持续填充
                    if avg_interval < 1.0:
                        print(f"✅ 填充模式验证通过：虚拟玩家持续填充队列")
                    else:
                        print(f"❌ 填充模式验证失败：虚拟玩家填充不够频繁")

                else:
                    mode = "稀疏模式"
                    expected_interval = stage_for_real_players
                    print(f"模式: {mode}")
                    print(f"预期插入间隔: {expected_interval}")
                    print(f"实际平均间隔: {avg_interval:.2f}")

                    # 稀疏模式验证
                    tolerance = 2.0  # 允许的误差范围
                    if abs(avg_interval - expected_interval) <= tolerance:
                        print(f"✅ 稀疏模式验证通过 (误差在{tolerance}以内)")
                    else:
                        print(f"❌ 稀疏模式验证失败 (误差超过{tolerance})")
        else:
            print("没有发现虚拟玩家游戏记录")
        
        conn.close()
        
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        print("虚拟玩家插入时机验证")
        print("=" * 50)
        analyze_game_timing()
    except Exception as e:
        print(f"脚本执行出错: {e}")
        import traceback
        traceback.print_exc()
