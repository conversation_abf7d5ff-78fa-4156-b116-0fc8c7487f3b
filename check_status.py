import sqlite3

print("开始检查数据库状态...")

try:
    conn = sqlite3.connect('Play_db.db')
    cursor = conn.cursor()

    # 检查表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"数据库中的表: {[t[0] for t in tables]}")

    # 检查队列状态
    cursor.execute('SELECT COUNT(*) FROM queue')
    total = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM queue WHERE player_id LIKE "Virtual%"')
    virtual = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM games')
    games = cursor.fetchone()[0]

    print(f'队列总人数: {total}')
    print(f'虚拟玩家: {virtual}')
    print(f'真实玩家: {total - virtual}')
    print(f'游戏次数: {games}')

    # 检查queue表结构
    cursor.execute("PRAGMA table_info(queue)")
    columns = cursor.fetchall()
    print(f'\nqueue表结构: {[col[1] for col in columns]}')

    # 显示队列中的所有玩家
    cursor.execute('SELECT * FROM queue ORDER BY priority')
    all_players = cursor.fetchall()

    print(f'\n队列详情:')
    for row in all_players:
        player_type = "虚拟" if 'Virtual' in str(row) else "真实"
        try:
            print(f'  [{player_type}] {row}')
        except UnicodeEncodeError:
            print(f'  [{player_type}] [编码问题] {row}')

    # 检查players表中的真实玩家
    cursor.execute('SELECT COUNT(*) FROM players WHERE player_id NOT LIKE "Virtual%"')
    real_players_count = cursor.fetchone()[0]
    print(f'\nplayers表中真实玩家数量: {real_players_count}')

    if real_players_count > 0:
        cursor.execute('SELECT name, player_id FROM players WHERE player_id NOT LIKE "Virtual%" LIMIT 5')
        real_players = cursor.fetchall()
        print('真实玩家列表:')
        for name, player_id in real_players:
            print(f'  {name} ({player_id})')

    # 检查真实玩家的游戏记录
    cursor.execute('SELECT COUNT(*) FROM games WHERE player_id NOT LIKE "Virtual%"')
    real_games_count = cursor.fetchone()[0]
    print(f'\n真实玩家游戏记录数量: {real_games_count}')

    if real_games_count > 0:
        cursor.execute('SELECT player_id, result, game_time FROM games WHERE player_id NOT LIKE "Virtual%" ORDER BY game_time DESC')
        real_games = cursor.fetchall()
        print('真实玩家游戏记录:')
        for player_id, result, game_time in real_games:
            print(f'  {player_id} - 结果: {result} - 时间: {game_time}')

    # 显示最近的游戏记录
    cursor.execute('SELECT player_id, result, game_time FROM games ORDER BY game_time DESC LIMIT 10')
    recent_games = cursor.fetchall()

    print(f'\n最近10次游戏记录:')
    for player_id, result, game_time in recent_games:
        player_type = "虚拟" if 'Virtual' in player_id else "真实"
        print(f'  [{player_type}] {player_id} - 结果: {result} - 时间: {game_time}')

    conn.close()
    print("数据库检查完成")

except Exception as e:
    print(f'错误: {e}')
    import traceback
    traceback.print_exc()
